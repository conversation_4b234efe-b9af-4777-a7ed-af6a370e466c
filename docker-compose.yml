version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: law_gpt
      POSTGRES_USER: law_user
      POSTGRES_PASSWORD: law_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - law-gpt-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - law-gpt-network

  # 法律问答系统应用
  law-gpt-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************************/law_gpt
      - REDIS_URL=redis://redis:6379/0
      - QWEN_MODEL_PATH=/app/models
    volumes:
      - ./app:/app/app
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - postgres
      - redis
    networks:
      - law-gpt-network

volumes:
  postgres_data:
  redis_data:

networks:
  law-gpt-network:
    driver: bridge
