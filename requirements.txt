# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# <PERSON><PERSON><PERSON><PERSON> and AI
langchain==0.1.0
langchain-community==0.0.10
langchain-core==0.1.0

# LlamaIndex and AI
llama-index==0.9.15
llama-index-embeddings-huggingface==0.1.4
llama-index-llms-huggingface==0.1.3
llama-index-vector-stores-faiss==0.1.2
llama-index-vector-stores-milvus==0.1.6

# AI Models
transformers==4.36.0
torch==2.1.0
sentence-transformers==2.2.2

# Vector Database and Search
faiss-cpu==1.7.4
pymilvus==2.3.4
numpy==1.24.3

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1

# Cache
redis==5.0.1
hiredis==2.2.3

# Document Processing
pypdf2==3.0.1
python-docx==1.1.0
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
httpx==0.25.2
aiofiles==23.2.1

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Monitoring
prometheus-client==0.19.0
